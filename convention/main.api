syntax = "v1"

import "base.api"
import "business_dictionary.api"
import "signature.api"
import "book.api"
import "internal_document.api"
import "external_document.api"
import "document_library.api"
import "workflow_pre_callback.api"

@server (
	group:      business_dictionary
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler getBusinessDictionarys
	get /business-dictionary/list (GetBusinessDictionarysReq) returns (GetBusinessDictionarysResp)

	@handler createBusinessDictionaryNode
	post /business-dictionary/node/create (CreateBusinessDictionaryNodeReq) returns (CreateBusinessDictionaryNodeResp)

	@handler getBusinessDictionaryNodes
	get /business-dictionary/node/list (GetBusinessDictionaryNodesReq) returns (GetBusinessDictionaryNodesResp)

	@handler updateBusinessDictionaryNode
	post /business-dictionary/node/update (UpdateBusinessDictionaryNodeReq) returns (UpdateBusinessDictionaryNodeResp)

	@handler moveBusinessDictionaryNode
	post /business-dictionary/node/move (MoveBusinessDictionaryNodeReq) returns (MoveBusinessDictionaryNodeResp)

	@handler deleteBusinessDictionaryNode
	post /business-dictionary/node/delete (DeleteBusinessDictionaryNodeReq) returns (DeleteBusinessDictionaryNodeResp)

	@handler getBusinessDictionaryRelationCount
	get /business-dictionary/node/relation/count (GetBusinessDictionaryRelationCountReq) returns (GetBusinessDictionaryRelationCountResp)

	@handler GetBusinessDictionaryNodeTree
	get /business-dictionary/node/tree (GetBusinessDictionaryNodeTreeReq) returns (GetBusinessDictionaryNodeTreeResp)
}

@server (
	group:      signature
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateSignatureTask
	post /signature-task (CreateSignatureTaskReq) returns (CreateSignatureTaskResp)

	@handler GetSignatureTaskStatus
	get /signature-task/status (GetSignatureTaskStatusReq) returns (GetSignatureTaskStatusResp)

	@handler UploadSignature
	post /signature/upload (UploadSignatureReq) returns (UploadSignatureResp)

	@handler GetCurrentSignature
	get /signatures/current returns (GetCurrentSignatureResp)

	@handler GetSignatureHistory
	get /signatures/history (GetSignatureHistoryReq) returns (GetSignatureHistoryResp)
}

@server (
	group:      file_management_books
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	// 新建书籍信息
	@handler CreateBook
	post /book/create (CreateBookReq) returns (CreateBookResp)

	@handler GetBookList
	post /book/list (GetBookListReq) returns (GetBookListResp)

	@handler UpdateBook
	post /book/update (BookInfo) returns (UpdateBookResp)

	@handler DeleteBook
	post /book/delete (DeleteBookReq) returns (DeleteBookResp)

	@handler ImportBookInfo
	post /book/import (ImportBookReq) returns (ImportBookResp)
}

@server (
	group:      internaldocument
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateInternalDocument
	post /internal/document/create (CreateInternalDocumentReq) returns (CreateInternalDocumentResp)

	@handler GetInternalDocument
	get /internal/document (GetInternalDocumentReq) returns (GetInternalDocumentResp)

	@handler ChangeInternalDocument
	post /internal/document/change (ChangeInternalDocumentReq) returns (ChangeInternalDocumentResp)

	@handler GetInternalDocuments
	post /internal/documents (GetInternalDocumentsReq) returns (GetInternalDocumentsResp)
}

@server (
	group:      externaldocument
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateExternalDocument
	post /external/document/create (CreateExternalDocumentReq) returns (CreateExternalDocumentResp)

	@handler GetExternalDocument
	get /external/document (GetExternalDocumentReq) returns (ExternalDocumentInfo)

	@handler ChangeExternalDocument
	post /external/document/change (ChangeExternalDocumentReq) returns (ChangeExternalDocumentResp)

	@handler GetExternalDocuments
	post /external/documents (GetExternalDocumentsReq) returns (GetExternalDocumentsResp)

	@handler PlagiarismCheck
	post /external/import/company/plagiarism-check (PlagiarismCheckReq) returns (PlagiarismCheckResp)
}

@server (
	group:      document_library
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler ImportDocumentLibrary
	post /document-library/import (ImportDocumentLibraryReq) returns (ImportDocumentLibraryResp)

	@handler ExportDocumentLibrary
	post /document-library/export (ExportDocumentLibraryReq) returns (ExportDocumentLibraryResp)

	@handler GetDocPermissionUsers
	get /document-library/permission/users (GetDocPermissionUsersReq) returns (GetDocPermissionUsersResp)
}

@server (
	group:  document_library
	prefix: /nebula/api/v1
)
service nebula {
	// 工作流前置回调接口：文档库发放
	@handler WorkflowPreCallback
	post /workflow/pre-callback/distribute (DistributeReq) returns (DistributeResp)
}

