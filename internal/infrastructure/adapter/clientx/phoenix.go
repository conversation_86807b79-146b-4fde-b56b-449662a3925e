package clientx

import (
	"context"
	"nebula/internal/infrastructure/adapter/clientx/entity"
)

//go:generate mockgen -source=phoenix.go -destination=mock_phoenix.go -package=clientx

// PhoenixClient 定义了与 Phoenix 服务交互的接口
type PhoenixClient interface {
	// 查询用户昵称
	GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error)

	// 查询文件信息
	GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error)

	// 获取审批流信息
	GetWorkflow(ctx context.Context, workflowId string) (progress entity.GetWorkflowRespData, err error)

	// 获取组织信息
	GetOrganizationInfo(ctx context.Context, orgId string) (organizationInfo entity.OrganizationInfo, err error)

	// 批量获取部门信息
	GetOrganizationInfoByCodes(ctx context.Context, codes []string) ([]entity.OrganizationInfo, error)

	// 批量获取用户信息
	GetUserInfoByMobiles(ctx context.Context, mobiles []string) ([]entity.UserInfo, error)

	// UploadFile 根据本地文件路径上传文件，可指定文件名，返回 fileId 和错误信息
	UploadFile(ctx context.Context, filePath, fileName string) (fileId string, err error)
}
