// Code generated by MockGen. DO NOT EDIT.
// Source: phoenix.go

// Package clientx is a generated GoMock package.
package clientx

import (
	context "context"
	entity "nebula/internal/infrastructure/adapter/clientx/entity"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockPhoenixClient is a mock of PhoenixClient interface.
type MockPhoenixClient struct {
	ctrl     *gomock.Controller
	recorder *MockPhoenixClientMockRecorder
}

// MockPhoenixClientMockRecorder is the mock recorder for MockPhoenixClient.
type MockPhoenixClientMockRecorder struct {
	mock *MockPhoenixClient
}

// NewMockPhoenixClient creates a new mock instance.
func NewMockPhoenixClient(ctrl *gomock.Controller) *MockPhoenixClient {
	mock := &MockPhoenixClient{ctrl: ctrl}
	mock.recorder = &MockPhoenixClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPhoenixClient) EXPECT() *MockPhoenixClientMockRecorder {
	return m.recorder
}

// GetFileInfo mocks base method.
func (m *MockPhoenixClient) GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileInfo", ctx, fileId)
	ret0, _ := ret[0].(entity.FileInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileInfo indicates an expected call of GetFileInfo.
func (mr *MockPhoenixClientMockRecorder) GetFileInfo(ctx, fileId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileInfo", reflect.TypeOf((*MockPhoenixClient)(nil).GetFileInfo), ctx, fileId)
}

// GetOrganizationInfo mocks base method.
func (m *MockPhoenixClient) GetOrganizationInfo(ctx context.Context, orgId string) (entity.OrganizationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationInfo", ctx, orgId)
	ret0, _ := ret[0].(entity.OrganizationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrganizationInfo indicates an expected call of GetOrganizationInfo.
func (mr *MockPhoenixClientMockRecorder) GetOrganizationInfo(ctx, orgId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationInfo", reflect.TypeOf((*MockPhoenixClient)(nil).GetOrganizationInfo), ctx, orgId)
}

// GetOrganizationInfoByCodes mocks base method.
func (m *MockPhoenixClient) GetOrganizationInfoByCodes(ctx context.Context, codes []string) ([]entity.OrganizationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationInfoByCodes", ctx, codes)
	ret0, _ := ret[0].([]entity.OrganizationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrganizationInfoByCodes indicates an expected call of GetOrganizationInfoByCodes.
func (mr *MockPhoenixClientMockRecorder) GetOrganizationInfoByCodes(ctx, codes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationInfoByCodes", reflect.TypeOf((*MockPhoenixClient)(nil).GetOrganizationInfoByCodes), ctx, codes)
}

// GetUserInfoByMobiles mocks base method.
func (m *MockPhoenixClient) GetUserInfoByMobiles(ctx context.Context, mobiles []string) ([]entity.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfoByMobiles", ctx, mobiles)
	ret0, _ := ret[0].([]entity.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfoByMobiles indicates an expected call of GetUserInfoByMobiles.
func (mr *MockPhoenixClientMockRecorder) GetUserInfoByMobiles(ctx, mobiles interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfoByMobiles", reflect.TypeOf((*MockPhoenixClient)(nil).GetUserInfoByMobiles), ctx, mobiles)
}

// GetUserNicknames mocks base method.
func (m *MockPhoenixClient) GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNicknames", ctx, uids)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNicknames indicates an expected call of GetUserNicknames.
func (mr *MockPhoenixClientMockRecorder) GetUserNicknames(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNicknames", reflect.TypeOf((*MockPhoenixClient)(nil).GetUserNicknames), ctx, uids)
}

// GetWorkflow mocks base method.
func (m *MockPhoenixClient) GetWorkflow(ctx context.Context, workflowId string) (entity.GetWorkflowRespData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", ctx, workflowId)
	ret0, _ := ret[0].(entity.GetWorkflowRespData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockPhoenixClientMockRecorder) GetWorkflow(ctx, workflowId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockPhoenixClient)(nil).GetWorkflow), ctx, workflowId)
}

// UploadFile mocks base method.
func (m *MockPhoenixClient) UploadFile(ctx context.Context, filePath, fileName string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadFile", ctx, filePath, fileName)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFile indicates an expected call of UploadFile.
func (mr *MockPhoenixClientMockRecorder) UploadFile(ctx, filePath, fileName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFile", reflect.TypeOf((*MockPhoenixClient)(nil).UploadFile), ctx, filePath, fileName)
}
