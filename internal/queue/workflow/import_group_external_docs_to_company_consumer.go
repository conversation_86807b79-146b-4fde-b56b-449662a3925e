package workflow

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

type ImportGroupExternalDocsToCompanyApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewImportGroupExternalDocsToCompanyApprovalConsumer(svcCtx *svc.ServiceContext) *ImportGroupExternalDocsToCompanyApprovalConsumer {
	return &ImportGroupExternalDocsToCompanyApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) Name() string {
	return "import_group_external_docs_to_company_approval"
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)
	orgInfo, err := h.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, msg.OrganizationID)
	if err != nil {
		logc.Errorf(ctx, "获取组织信息失败: %v", err)
		return
	}

	var data Data
	if err = json.Unmarshal([]byte(msg.FormContent), &data); err != nil {
		logc.Errorf(ctx, "处理表单信息失败: %v", err)
		return
	}
	var importGroupDocsToCompanyInfo []*docvault.ImportGroupDocsToCompanyInfo
	for _, v := range data.Data.Data {
		importGroupDocsToCompanyInfo = append(importGroupDocsToCompanyInfo, &docvault.ImportGroupDocsToCompanyInfo{
			Id:              v.ID,
			OriginalNumber:  v.OriginalNumber,
			OriginalVersion: v.OriginalVersion,
		})
	}
	_, err = docvault.NewExternalDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ImportGroupDocsToCompany(ctx, &docvault.ImportGroupDocsToCompanyReq{
		OrgCode: orgInfo.Code,
		Data:    importGroupDocsToCompanyInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
	}
}

type Data struct {
	Data ImportGroupExternalDocsToCompany `json:"data"`
}

type ImportGroupExternalDocsToCompany struct {
	Data []ImportGroupExternalDocsToCompanyInfo `json:"data"`
}

type ImportGroupExternalDocsToCompanyInfo struct {
	ID              string `json:"id"`
	OriginalNumber  string `json:"originalNumber"`
	OriginalVersion string `json:"originalVersion"`
}
