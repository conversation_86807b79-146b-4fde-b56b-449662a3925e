package document_library

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackLogic {
	return &WorkflowPreCallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WorkflowPreCallbackLogic) WorkflowPreCallback(req *types.DistributeReq) (resp *types.DistributeResp, err error) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)
	// 处理表单信息
	var distributeApplication DistributeApplication
	if err = json.Unmarshal([]byte(req.FormContent), &distributeApplication); err != nil {
		l.Logger.Errorf("处理表单信息失败: %v", err)
		return nil, err
	}

	// 获取文件分类
	fileCategory, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, distributeApplication.TypeDictNodeId)
	if err != nil {
		l.Logger.Errorf("获取文件分类失败: %v", err)
		return nil, err
	}

	// 构造发放记录
	documentDistributeReq := l.apiReqToRpcReq(&distributeApplication, &fileCategory)
	documentDistributeReq.WorkflowId = req.WorkflowID

	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).PreSaveDistributeRecord(l.ctx, documentDistributeReq)
	if err != nil {
		logc.Errorf(l.ctx, "保存文件发放记录失败: %v", err)
		return nil, err
	}

	return nil, nil
}

func (l *WorkflowPreCallbackLogic) apiReqToRpcReq(distributeApplication *DistributeApplication, fileCategory *mapper.BusinessDictionaryNodeRelation) *docvault.DocumentDistributeReq {
	documentDistributeReq := &docvault.DocumentDistributeReq{
		Applicant:          distributeApplication.Applicant,
		ApplyDate:          distributeApplication.ApplyDate,
		DistributeType:     int32(distributeApplication.DistributeType),
		FileType:           int32(distributeApplication.FileType),
		TypeDictNodeId:     distributeApplication.TypeDictNodeId,
		Reason:             distributeApplication.Reason,
		OtherReason:        distributeApplication.OtherReason,
		WishDistributeDate: distributeApplication.WishDistributeDate,
		FileCategory:       fileCategory.Names,
		DistributeList:     nil,
	}

	for k1, distribute := range distributeApplication.DistributeList {
		documentDistributeReq.DistributeList = append(documentDistributeReq.DistributeList, &docvault.DistributeList{
			FileId:      distribute.FileID,
			FileName:    distribute.FileName,
			Number:      distribute.Number,
			Version:     distribute.Version,
			Permissions: nil,
		})
		for k2, permission := range distribute.Permissions {
			documentDistributeReq.DistributeList[k1].Permissions = append(documentDistributeReq.DistributeList[k1].Permissions, &docvault.Permission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				Recipient:      permission.Recipient,
				ReceivedBy:     nil,
			})
			for _, received := range permission.ReceivedBy {
				documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy = append(documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy, &docvault.Recipient{
					UserId:   received.UserId,
					UserName: received.UserName,
				})
			}
		}
	}
	return documentDistributeReq
}

type DistributeApplication struct {
	Applicant          string           `json:"applicant"`          // 申请人
	ApplyDate          int64            `json:"applyDate"`          // 申请日期
	DistributeType     int              `json:"distributeType"`     // 发放类型
	FileType           int              `json:"fileType"`           // 文件类型
	TypeDictNodeId     string           `json:"typeDictNodeId"`     // 类型字典节点id
	Reason             string           `json:"reasonDictNodeId"`   // 原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放日期
	DistributeList     []DistributeItem `json:"distributeList"`     // 发放清单
}

type DistributeItem struct {
	FileID      string       `json:"fileID"`      // 文件id
	FileName    string       `json:"fileName"`    // 文件名
	Number      string       `json:"number"`      // 文件编号
	Version     string       `json:"version"`     // 版本
	Permissions []Permission `json:"permissions"` // 权限
}

type Permission struct {
	FileForm       int         `json:"fileForm"`       // 文件形式
	FilePermission int         `json:"filePermission"` // 文件权限
	Recipient      string      `json:"recipient"`      // 接收方
	ReceivedBy     []Recipient `json:"receivedBy"`     // 接收人
}

type Recipient struct {
	UserId   string `json:"userId"`
	UserName string `json:"userName"`
}
