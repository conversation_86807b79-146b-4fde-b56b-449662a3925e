package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocPermissionUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDocPermissionUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocPermissionUsersLogic {
	return &GetDocPermissionUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDocPermissionUsersLogic) GetDocPermissionUsers(req *types.GetDocPermissionUsersReq) (resp *types.GetDocPermissionUsersResp, err error) {
	user, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDocPermissionUsers(l.ctx, &docvault.GetDocPermissionUsersReq{
		FileId:         req.FileID,
		FileFrom:       req.FileForm,
		FilePermission: req.FilePermission,
	})
	if err != nil {
		l.Logger.Errorf("获取文件权限关联用户失败：%v", err)
		return nil, err
	}

	return &types.GetDocPermissionUsersResp{UserIds: user.UserIds}, nil
}
