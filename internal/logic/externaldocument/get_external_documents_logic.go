package externaldocument

import (
	"context"
	"fmt"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDocumentsLogic {
	return &GetExternalDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExternalDocumentsLogic) GetExternalDocuments(req *types.GetExternalDocumentsReq) (resp *types.GetExternalDocumentsResp, err error) {
	// 查询组织信息
	organizationInfo, err := GetOrgInfo(l.ctx, l.svcCtx, req.OrgType)
	if err != nil {
		return nil, err
	}

	pageInfo := &docvault.PageInfo{
		PageSize: int32(req.PageSize),
		Page:     int32(req.Page),
		NoPage:   req.NoPage,
	}
	page, err := docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Page(l.ctx, &docvault.ExternalDocumentPageReq{
		PageInfo:                       pageInfo,
		OrgType:                        int32(req.OrgType),
		Number:                         req.Number,
		Name:                           req.Name,
		OriginalNumber:                 req.OriginalNumber,
		OriginalDocNumber:              req.OriginalDocNumber,
		PublishDocNumber:               req.PublishDocNumber,
		TypeDictionaryNodeIds:          req.TypeDictionaryNodeIds,
		DomainDictionaryNodeId:         req.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeId: req.AuthenticationDictionaryNodeId,
		BeAttachedFile:                 fmt.Sprintf("%d", req.BeAttachedFile),
		Status:                         int32(req.Status),
		OrgId:                          organizationInfo.Id,
	})
	if err != nil {
		l.Logger.Errorf("查询列表信息失败：%v", err)
		return nil, err
	}

	data := l.rpcToApi(page)

	return &types.GetExternalDocumentsResp{
		PageInfo: types.PageInfo{
			Total: uint64(page.Total),
		},
		Data: data,
	}, nil
}

func (l *GetExternalDocumentsLogic) rpcToApi(page *docvault.ExternalDocumentPageResp) (externalDocInfo []types.ExternalDocumentInfo) {
	for _, v := range page.Data {
		auditors, approvers := l.buildApprovalInfo(v)
		externalDocInfo = append(externalDocInfo, types.ExternalDocumentInfo{
			ID:                v.Id,
			Number:            v.Number,
			Version:           v.Version,
			OriginalNumber:    v.OriginalNumber,
			OriginalVersion:   v.OriginalVersion,
			Name:              v.Name,
			DocType:           v.DocType,
			Domain:            v.Domain,
			Authentication:    v.Authentication,
			OriginalDocNumber: v.OriginalDocNumber,
			PublishDocNumber:  v.PublishDocNumber,
			PublishDepartment: v.PublishDepartment,
			ApprovalInfo: types.ApprovalInfo{
				Auditors:  auditors,
				Approvers: approvers,
			},
			PublishDate:                     v.PublishDate,
			EffectiveDate:                   v.EffectiveDate,
			Status:                          int(v.Status),
			TypeDictionaryNodeId:            v.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          v.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: v.AuthenticationDictionaryNodeIds,
			FileInfo: types.FileInfo{
				FileID:   v.FileId,
				FileName: l.svcCtx.QuickNameTranslator.TranslateFileName(l.ctx, v.FileId),
			},
		})
	}
	return externalDocInfo
}

func (l *GetExternalDocumentsLogic) buildApprovalInfo(rs *docvault.ExternalDocumentPageInfo) ([]types.Approval, []types.Approval) {
	if rs.ApprovalInfo == nil {
		return nil, nil
	}
	auditors := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	approvers := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	return auditors, approvers
}
