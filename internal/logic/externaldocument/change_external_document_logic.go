package externaldocument

import (
	"context"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"
	"time"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeExternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewChangeExternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeExternalDocumentLogic {
	return &ChangeExternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChangeExternalDocumentLogic) ChangeExternalDocument(req *types.ChangeExternalDocumentReq) (resp *types.ChangeExternalDocumentResp, err error) {
	organizationInfo, err := GetOrgInfo(l.ctx, l.svcCtx, req.OrgType)
	if err != nil {
		return nil, err
	}
	// 设置文档编号、文档类型、领域类型和认证类型
	credential, err := GetCredential(req.TypeDictionaryNodeId, req.DomainDictionaryNodeId, req.AuthenticationDictionaryNodeIds, l.svcCtx.NebulaDB, l.ctx, organizationInfo.Code)
	if err != nil {
		return nil, err
	}

	_, err = docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Change(l.ctx, &docvault.ExternalDocumentChangeReq{
		Id:                              req.ID,
		Name:                            req.Name,
		OriginalDocNumber:               req.OriginalNumber,
		PublishDocNumber:                req.PublishDocNumber,
		PublishDepartment:               req.PublishDepartment,
		TypeDictionaryNodeId:            req.TypeDictionaryNodeId,
		DomainDictionaryNodeId:          req.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeIds: req.AuthenticationDictionaryNodeIds,
		PublishDate:                     req.PublishDate,
		EffectiveDate:                   req.EffectiveDate,
		OriginalNumber:                  req.OriginalDocNumber,
		OriginalVersion:                 req.OriginalVersion,
		FileId:                          req.FileId,
		NumberPrefix:                    req.OriginalNumber,
		DocType:                         credential.DocType,
		Authentications:                 credential.Authentications,
		Domain:                          credential.Domain,
	})
	if err != nil {
		return nil, err
	}

	// 更新业务字典关系
	err = l.updateBusinessDictionaryRelation(req)
	if err != nil {
		l.Logger.Errorf("更新业务字典关系错误：%v", err)
		return nil, err
	}

	return &types.ChangeExternalDocumentResp{}, nil
}

func (l *ChangeExternalDocumentLogic) updateBusinessDictionaryRelation(req *types.ChangeExternalDocumentReq) (err error) {
	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)
	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).TxUpdateBusinessDictionaryRelationByBusinessID(l.ctx, req.ID, consts.BusinessDictionaryBusinessTypeExternalDocumentCategory, req.TypeDictionaryNodeId, tx)
	if err != nil {
		return err
	}
	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).TxUpdateBusinessDictionaryRelationByBusinessID(l.ctx, req.ID, consts.BusinessDictionaryBusinessTypeExternalDocumentDomain, req.DomainDictionaryNodeId, tx)
	if err != nil {
		return err
	}
	// 先删除
	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).TxDeleteBusinessDictionaryRelationByBusinessIDAndType(l.ctx, req.ID, consts.BusinessDictionaryBusinessTypeExternalDocumentAuthentication, tx)
	if err != nil {
		return err
	}
	if len(req.AuthenticationDictionaryNodeIds) == 0 {
		return nil
	}
	// 再添加
	var businessDictionaryRelation []mapper.BusinessDictionaryRelation
	for _, v := range req.AuthenticationDictionaryNodeIds {
		businessDictionaryRelation = append(businessDictionaryRelation, mapper.BusinessDictionaryRelation{
			ID:               l.svcCtx.IdGenerator.GenerateIDString(),
			DictionaryNodeID: v,
			BusinessID:       req.ID,
			BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentAuthentication,
			CreatedAt:        time.Now(),
			CreatedBy:        utils.GetContextUserID(l.ctx),
		})
	}
	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).TxBatchCreateBusinessDictionaryRelation(l.ctx, businessDictionaryRelation, tx)
	if err != nil {
		return err
	}
	return nil
}
