package business_dictionary

import (
	"context"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type MoveBusinessDictionaryNodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMoveBusinessDictionaryNodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoveBusinessDictionaryNodeLogic {
	return &MoveBusinessDictionaryNodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MoveBusinessDictionaryNodeLogic) MoveBusinessDictionaryNode(req *types.MoveBusinessDictionaryNodeReq) (resp *types.MoveBusinessDictionaryNodeResp, err error) {
	node, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetById(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}
	node.Sort = req.Sort

	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodesByDictionaryIDAndParentID(l.ctx, node.DictionaryID, node.ParentID)
	if err != nil {
		return nil, err
	}

	// 对指定循序后面的节点进行排序，忽略当前节点
	nodesToUpdate := l.sortNodes(node, nodes, req)

	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)

	if err = l.updateNodes(nodesToUpdate, tx); err != nil {
		return nil, err
	}

	err = mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryUpdatedAt(l.ctx, tx, node.DictionaryID, time.Now(), utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	return &types.MoveBusinessDictionaryNodeResp{}, err
}

// sortNodes 对节点进行排序调整，根据移动方向正确处理节点位置
func (*MoveBusinessDictionaryNodeLogic) sortNodes(targetNode mapper.BusinessDictionaryNode, nodes []mapper.BusinessDictionaryNode, req *types.MoveBusinessDictionaryNodeReq) []mapper.BusinessDictionaryNode {
	nodesToUpdate := []mapper.BusinessDictionaryNode{}

	// 获取目标节点的原始排序位置
	originalSort := targetNode.Sort
	newSort := req.Sort

	// 设置目标节点的新排序位置
	targetNode.Sort = newSort
	nodesToUpdate = append(nodesToUpdate, targetNode)

	// 根据移动方向调整其他节点的排序
	for _, currentNode := range nodes {
		// 跳过目标节点本身，避免重复处理
		if currentNode.ID == req.ID {
			continue
		}

		if originalSort < newSort {
			// 向后移动：原位置在目标位置之间的节点需要向前移动
			if currentNode.Sort > originalSort && currentNode.Sort <= newSort {
				currentNode.Sort--
				nodesToUpdate = append(nodesToUpdate, currentNode)
			}
		} else if originalSort > newSort {
			// 向前移动：目标位置到原位置之间的节点需要向后移动
			if currentNode.Sort >= newSort && currentNode.Sort < originalSort {
				currentNode.Sort++
				nodesToUpdate = append(nodesToUpdate, currentNode)
			}
		}
		// 如果 originalSort == newSort，则不需要调整任何节点
	}

	return nodesToUpdate
}

func (l *MoveBusinessDictionaryNodeLogic) updateNodes(nodesToUpdate []mapper.BusinessDictionaryNode, tx *mapper.NebulaTX) (err error) {
	for _, node := range nodesToUpdate {
		err = mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryNodeWithTransaction(l.ctx, node, tx)
		if err != nil {
			return err
		}
	}
	return nil
}
