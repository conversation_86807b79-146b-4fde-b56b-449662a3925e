package business_dictionary

import (
	"context"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBusinessDictionaryNodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateBusinessDictionaryNodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBusinessDictionaryNodeLogic {
	return &UpdateBusinessDictionaryNodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBusinessDictionaryNodeLogic) UpdateBusinessDictionaryNode(req *types.UpdateBusinessDictionaryNodeReq) (resp *types.UpdateBusinessDictionaryNodeResp, err error) {
	node, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetById(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}
	oldName := node.Name
	oldCode := node.Code
	node.Status = req.Status
	node.Name = req.Name
	node.Remark = req.Remark
	node.UpdatedAt = time.Now()
	node.UpdatedBy = utils.GetContextUserID(l.ctx)
	node.Code = req.Code
	nodeClient := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB)

	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)

	err = nodeClient.UpdateBusinessDictionaryNode(l.ctx, tx, node)
	if err != nil {
		return nil, err
	}

	err = mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryUpdatedAt(l.ctx, tx, node.DictionaryID, node.UpdatedAt, utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	nodeIDs, err := nodeClient.GetBusinessDictionaryIDsByParentID(l.ctx, node.ID)
	if err != nil {
		return resp, err
	}

	nodeIDs = append(nodeIDs, node.ID) // 包含自身

	// 递归获取自身及所有子节点
	err = l.saveNodeRelations(nodeClient, nodeIDs, tx)
	if err != nil {
		return nil, err
	}
	if oldName != req.Name || oldCode != req.Code {
		// 发送消息到kafka
		err = l.sendKafkaMessage(nodeIDs)
		if err != nil {
			return nil, err
		}
	}
	// 查询受影响的业务字典节点
	return &types.UpdateBusinessDictionaryNodeResp{}, err
}

func (l *UpdateBusinessDictionaryNodeLogic) sendKafkaMessage(nodeIDs []string) error {
	err := l.svcCtx.BusinessDictionaryChangeProducer.SendMessage(l.ctx, nodeIDs)
	if err != nil {
		logc.Errorf(l.ctx, "send BusinessDictionaryChangeProducer message error: %v", err)
		return err
	}
	return nil
}

func (l *UpdateBusinessDictionaryNodeLogic) saveNodeRelations(nodeClient *mapper.BusinessDictionaryNodeClient, nodeIDs []string, tx *mapper.NebulaTX) error {

	nodes, err := nodeClient.GetBusinessDictionaryNodeByIDs(l.ctx, nodeIDs)
	if err != nil {
		return err
	}
	ancestryMap := make(map[string][]mapper.BusinessDictionaryNode)
	for _, n := range nodes {
		ancestry, err := nodeClient.GetNodeAncestry(l.ctx, tx, n.ID)
		if err != nil {
			return err
		}
		ancestryMap[n.ID] = ancestry
	}
	relations := buildNodeRelations(nodes, ancestryMap)

	if len(relations) > 0 {
		err = mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).BatchUpsertRelations(l.ctx, tx, relations)
		if err != nil {
			return err
		}
	}
	return nil
}
